#!/usr/bin/env python3
import os
import struct

# Read the encrypted file
with open('desktop.enc', 'rb') as f:
    encrypted_data = f.read()

print(f"File size: {len(encrypted_data)} bytes")

# Let's analyze the structure
print("\nFirst 50 bytes (hex):")
print(' '.join(f'{b:02x}' for b in encrypted_data[:50]))

print("\nLast 50 bytes (hex):")
print(' '.join(f'{b:02x}' for b in encrypted_data[-50:]))

# Look for repeating patterns
print("\nLooking for repeating patterns...")
for pattern_len in [1, 2, 4, 8, 16]:
    patterns = {}
    for i in range(0, len(encrypted_data) - pattern_len + 1, pattern_len):
        pattern = encrypted_data[i:i+pattern_len]
        patterns[pattern] = patterns.get(pattern, 0) + 1
    
    # Find most common patterns
    common_patterns = sorted(patterns.items(), key=lambda x: x[1], reverse=True)[:5]
    if common_patterns[0][1] > 1:
        print(f"Pattern length {pattern_len}:")
        for pattern, count in common_patterns:
            if count > 1:
                print(f"  {pattern.hex()}: {count} times")

# Try to see if it's a simple cipher by looking at byte distribution
print("\nByte distribution analysis:")
from collections import Counter
byte_counts = Counter(encrypted_data)
print("Most common bytes:")
for byte, count in byte_counts.most_common(10):
    print(f"  0x{byte:02x} ({chr(byte) if 32 <= byte <= 126 else '?'}): {count} times")

# Check if the file size gives us any clues
print(f"\nFile size analysis:")
print(f"Size: {len(encrypted_data)} bytes")
print(f"Size % 16: {len(encrypted_data) % 16}")
print(f"Size % 8: {len(encrypted_data) % 8}")
print(f"Size % 4: {len(encrypted_data) % 4}")

# Try to see if there's a header or footer
print("\nChecking for potential headers/footers...")
# Common text file beginnings
text_starts = [b'The ', b'This ', b'A ', b'An ', b'In ', b'On ', b'At ', b'To ', b'For ']
for start in text_starts:
    for key in range(256):
        decrypted_start = bytes([b ^ key for b in encrypted_data[:len(start)]])
        if decrypted_start == start:
            print(f"Possible XOR key {key} (0x{key:02x}) - starts with '{start.decode()}'")
            # Try full decryption
            full_decrypt = bytes([b ^ key for b in encrypted_data])
            try:
                text = full_decrypt.decode('utf-8')
                print(f"Full decryption: {text[:100]}...")
                with open(f'xor_decrypt_{key}.txt', 'w') as f:
                    f.write(text)
            except:
                pass

# Try multi-byte XOR keys
print("\nTrying multi-byte XOR keys...")
common_keys = [b'key', b'pass', b'flag', b'ctf', b'bsides', b'desktop', b'enc']
for key in common_keys:
    decrypted = bytearray()
    for i, byte in enumerate(encrypted_data):
        decrypted.append(byte ^ key[i % len(key)])
    
    try:
        text = bytes(decrypted).decode('utf-8')
        if any(word in text.lower() for word in ['flag', 'bsides', 'ctf', 'the', 'and', 'password']):
            print(f"Possible key '{key.decode()}': {text[:100]}...")
            with open(f'multixor_{key.decode()}.txt', 'w') as f:
                f.write(text)
    except:
        pass

# Try Caesar cipher on the entire file
print("\nTrying Caesar cipher...")
for shift in range(1, 26):
    decrypted = bytearray()
    for byte in encrypted_data:
        if 32 <= byte <= 126:  # Printable ASCII
            new_byte = ((byte - 32 + shift) % 95) + 32
        else:
            new_byte = byte
        decrypted.append(new_byte)
    
    try:
        text = bytes(decrypted).decode('utf-8')
        if any(word in text.lower() for word in ['flag', 'bsides', 'ctf', 'the', 'and']):
            print(f"Possible Caesar shift {shift}: {text[:100]}...")
            with open(f'caesar_{shift}.txt', 'w') as f:
                f.write(text)
    except:
        pass
