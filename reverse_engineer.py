#!/usr/bin/env python3
import os
import sys

# Since this is a CTF, let's try to reverse engineer by creating a simple encryption
# and see if we can figure out the pattern

# Read the encrypted file
with open('desktop.enc', 'rb') as f:
    encrypted_data = f.read()

print(f"Encrypted file size: {len(encrypted_data)} bytes")

# Let's assume this might be a simple substitution cipher or XOR
# Try to create a test file and see if we can find patterns

# Common CTF encryption patterns:
# 1. Simple XOR with a key
# 2. Caesar cipher
# 3. Substitution cipher
# 4. Base64 encoding
# 5. ROT13 or similar rotation

def try_caesar_cipher(data, shift):
    """Try Caesar cipher decryption"""
    result = bytearray()
    for byte in data:
        # Only shift printable ASCII characters
        if 32 <= byte <= 126:
            shifted = ((byte - 32 + shift) % 95) + 32
            result.append(shifted)
        else:
            result.append(byte)
    return bytes(result)

def try_substitution(data, key_map):
    """Try substitution cipher"""
    result = bytearray()
    for byte in data:
        result.append(key_map.get(byte, byte))
    return bytes(result)

# Try Caesar cipher with different shifts
print("Trying Caesar cipher...")
for shift in range(1, 26):
    decrypted = try_caesar_cipher(encrypted_data, shift)
    try:
        text = decrypted.decode('utf-8', errors='ignore')
        if any(word in text.lower() for word in ['flag', 'bsides', 'ctf', 'the', 'and', 'password']):
            print(f"Possible Caesar cipher with shift {shift}:")
            print(text[:200])
            print("=" * 50)
    except:
        pass

# Try simple byte operations
print("Trying simple byte operations...")
operations = [
    ("Add 1", lambda x: (x + 1) % 256),
    ("Subtract 1", lambda x: (x - 1) % 256),
    ("XOR with 0xFF", lambda x: x ^ 0xFF),
    ("XOR with 0xAA", lambda x: x ^ 0xAA),
    ("XOR with 0x55", lambda x: x ^ 0x55),
    ("Reverse bits", lambda x: int('{:08b}'.format(x)[::-1], 2)),
]

for name, operation in operations:
    try:
        decrypted = bytes([operation(b) for b in encrypted_data])
        text = decrypted.decode('utf-8', errors='ignore')
        if any(word in text.lower() for word in ['flag', 'bsides', 'ctf', 'the', 'and', 'password']):
            print(f"Possible decryption with {name}:")
            print(text[:200])
            print("=" * 50)
    except:
        pass

# Try to look for file signatures
print("Checking for file signatures...")
common_signatures = {
    b'\x89PNG': 'PNG image',
    b'\xFF\xD8\xFF': 'JPEG image',
    b'GIF8': 'GIF image',
    b'%PDF': 'PDF document',
    b'PK\x03\x04': 'ZIP archive',
    b'\x7fELF': 'ELF executable',
    b'MZ': 'Windows executable',
    b'#!/bin/': 'Shell script',
    b'#!/usr/bin/': 'Shell script',
    b'<?xml': 'XML document',
    b'<html': 'HTML document',
}

for sig, desc in common_signatures.items():
    if encrypted_data.startswith(sig):
        print(f"File appears to be: {desc}")
        break

# Try to see if it's a simple rotation of the entire file
print("Trying file rotation...")
for i in range(1, min(20, len(encrypted_data))):
    rotated = encrypted_data[i:] + encrypted_data[:i]
    for sig, desc in common_signatures.items():
        if rotated.startswith(sig):
            print(f"File might be {desc} with {i} byte rotation")
            with open(f'rotated_{i}.bin', 'wb') as f:
                f.write(rotated)
            break
