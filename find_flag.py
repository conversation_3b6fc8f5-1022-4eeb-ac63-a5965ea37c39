#!/usr/bin/env python3

# Let's try to find the flag by looking for common CTF flag formats
# Common formats: flag{...}, FLAG{...}, bsides{...}, BSides{...}

def xor_decrypt(data, key):
    if isinstance(key, str):
        key = key.encode()
    elif isinstance(key, int):
        key = bytes([key])
    
    result = bytearray()
    for i, byte in enumerate(data):
        result.append(byte ^ key[i % len(key)])
    return bytes(result)

# Read the encrypted file
with open('desktop.enc', 'rb') as f:
    encrypted_data = f.read()

print("Searching for flag patterns...")

# Try single-byte XOR keys
for key in range(256):
    decrypted = xor_decrypt(encrypted_data, key)
    try:
        text = decrypted.decode('utf-8', errors='ignore')
        
        # Look for flag patterns
        flag_patterns = ['flag{', 'FLAG{', 'bsides{', 'BSides{', 'ctf{', 'CTF{']
        for pattern in flag_patterns:
            if pattern in text:
                print(f"\n*** FOUND FLAG PATTERN '{pattern}' with XOR key {key} (0x{key:02x}) ***")
                print(f"Text: {text}")
                with open(f'flag_found_xor_{key}.txt', 'w') as f:
                    f.write(text)
                exit(0)
                
        # Also look for common words that might indicate success
        if any(word in text.lower() for word in ['congratulations', 'well done', 'success', 'correct']):
            print(f"\nPossible success message with XOR key {key}: {text[:100]}...")
            
    except:
        continue

# Try multi-byte XOR keys
print("\nTrying multi-byte XOR keys...")
common_keys = [
    'key', 'password', 'secret', 'flag', 'ctf', 'bsides', 'desktop', 'enc',
    'admin', 'root', 'user', 'test', '123', 'abc', 'xyz', 'qwerty',
    'bsides_blr', 'bangalore', 'india', 'challenge', 'reverse'
]

for key in common_keys:
    decrypted = xor_decrypt(encrypted_data, key)
    try:
        text = decrypted.decode('utf-8', errors='ignore')
        
        # Look for flag patterns
        flag_patterns = ['flag{', 'FLAG{', 'bsides{', 'BSides{', 'ctf{', 'CTF{']
        for pattern in flag_patterns:
            if pattern in text:
                print(f"\n*** FOUND FLAG PATTERN '{pattern}' with key '{key}' ***")
                print(f"Text: {text}")
                with open(f'flag_found_key_{key}.txt', 'w') as f:
                    f.write(text)
                exit(0)
                
    except:
        continue

# Try position-based XOR
print("\nTrying position-based XOR...")
position_functions = [
    lambda i: i % 256,
    lambda i: (i + 1) % 256,
    lambda i: (i * 2) % 256,
    lambda i: (i ^ 0xAA) % 256,
    lambda i: (i + 42) % 256,
    lambda i: (i * i) % 256,
    lambda i: ((i + 1) * (i + 1)) % 256,
]

for func_idx, func in enumerate(position_functions):
    decrypted = bytearray()
    for i, byte in enumerate(encrypted_data):
        key = func(i)
        decrypted.append(byte ^ key)
    
    try:
        text = bytes(decrypted).decode('utf-8', errors='ignore')
        
        # Look for flag patterns
        flag_patterns = ['flag{', 'FLAG{', 'bsides{', 'BSides{', 'ctf{', 'CTF{']
        for pattern in flag_patterns:
            if pattern in text:
                print(f"\n*** FOUND FLAG PATTERN '{pattern}' with position function {func_idx} ***")
                print(f"Text: {text}")
                with open(f'flag_found_pos_{func_idx}.txt', 'w') as f:
                    f.write(text)
                exit(0)
                
    except:
        continue

# Try Caesar cipher
print("\nTrying Caesar cipher...")
for shift in range(1, 26):
    decrypted = bytearray()
    for byte in encrypted_data:
        if 32 <= byte <= 126:  # Printable ASCII
            new_byte = ((byte - 32 + shift) % 95) + 32
        else:
            new_byte = byte
        decrypted.append(new_byte)
    
    try:
        text = bytes(decrypted).decode('utf-8', errors='ignore')
        
        # Look for flag patterns
        flag_patterns = ['flag{', 'FLAG{', 'bsides{', 'BSides{', 'ctf{', 'CTF{']
        for pattern in flag_patterns:
            if pattern in text:
                print(f"\n*** FOUND FLAG PATTERN '{pattern}' with Caesar shift {shift} ***")
                print(f"Text: {text}")
                with open(f'flag_found_caesar_{shift}.txt', 'w') as f:
                    f.write(text)
                exit(0)
                
    except:
        continue

print("\nNo flag patterns found with common methods.")
print("The encryption might be more complex or the flag format might be different.")
