#!/usr/bin/env python3
import base64
import zlib

# Read the complete base64 string from file
with open('extracted_b64.txt', 'r') as f:
    b64_string = f.read().strip()

print(f"Base64 string length: {len(b64_string)}")

# Decode the base64 string
try:
    decoded = base64.b64decode(b64_string.lstrip("="))
    print(f"Decoded length: {len(decoded)}")

    # Check if it's already text
    try:
        text = decoded.decode('utf-8')
        print("SUCCESS! It's already text:")
        print(text)
    except:
        # Examine the first few bytes
        print("First 50 bytes (hex):", decoded[:50].hex())
        print("First 50 bytes (ascii):", decoded[:50])

        # Try to save as binary file for further analysis
        with open('decoded_data.bin', 'wb') as f:
            f.write(decoded)
        print("Saved decoded data to decoded_data.bin")

except Exception as e:
    print(f"Error: {e}")
