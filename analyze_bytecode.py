#!/usr/bin/env python3
import marshal
import dis
import types

def analyze_pyc(filename):
    """Analyze a .pyc file and try to extract information"""
    print(f"Analyzing {filename}...")
    
    with open(filename, 'rb') as f:
        # Skip the header (magic number, timestamp, etc.)
        magic = f.read(4)
        print(f"Magic number: {magic.hex()}")
        
        # Skip additional header fields for Python 3.7+
        f.read(12)  # Skip timestamp, size, etc.
        
        try:
            # Load the code object
            code = marshal.load(f)
            print(f"Code object loaded successfully")
            print(f"Code name: {code.co_name}")
            print(f"Filename: {code.co_filename}")
            print(f"Constants: {code.co_consts}")
            print(f"Names: {code.co_names}")
            
            # Try to disassemble
            print("\nDisassembly:")
            dis.dis(code)
            
            # Look for string constants that might be the base64 data
            for i, const in enumerate(code.co_consts):
                if isinstance(const, str) and len(const) > 100:
                    print(f"\nLarge string constant {i} (length {len(const)}):")
                    print(f"First 100 chars: {const[:100]}")
                    print(f"Last 100 chars: {const[-100:]}")
                    
                    # Save it to a file for analysis
                    with open(f'constant_{i}.txt', 'w') as out:
                        out.write(const)
                    
                    # Try to decode if it looks like base64
                    if const.replace('+', '').replace('/', '').replace('=', '').isalnum():
                        try:
                            import base64
                            decoded = base64.b64decode(const)
                            print(f"Base64 decoded length: {len(decoded)}")
                            
                            # Try to decompress
                            try:
                                import zlib
                                decompressed = zlib.decompress(decoded)
                                print("Zlib decompression successful!")
                                print(decompressed.decode('utf-8'))
                                
                                with open(f'decompressed_{i}.py', 'w') as out:
                                    out.write(decompressed.decode('utf-8'))
                                return
                            except:
                                pass
                                
                            # Try as raw text
                            try:
                                text = decoded.decode('utf-8')
                                if 'def ' in text or 'import ' in text:
                                    print("Decoded as text:")
                                    print(text)
                                    with open(f'decoded_{i}.py', 'w') as out:
                                        out.write(text)
                                    return
                            except:
                                pass
                        except:
                            pass
            
        except Exception as e:
            print(f"Error loading code object: {e}")

# Analyze the main enc.pyc file
analyze_pyc('enc_extracted/enc.pyc')
