#!/usr/bin/env python3
import os

def try_xor_decrypt(data, key):
    """Try to decrypt data using XOR with a given key"""
    if isinstance(key, int):
        return bytes([b ^ key for b in data])
    elif isinstance(key, str):
        key_bytes = key.encode()
        return bytes([data[i] ^ key_bytes[i % len(key_bytes)] for i in range(len(data))])
    elif isinstance(key, bytes):
        return bytes([data[i] ^ key[i % len(key)] for i in range(len(data))])

def is_printable_text(data):
    """Check if data looks like printable text"""
    try:
        text = data.decode('utf-8')
        # Check if it contains mostly printable characters
        printable_count = sum(1 for c in text if c.isprintable() or c in '\n\r\t')
        return printable_count / len(text) > 0.8
    except:
        return False

# Read the encrypted file
with open('desktop.enc', 'rb') as f:
    encrypted_data = f.read()

print(f"Encrypted file size: {len(encrypted_data)} bytes")
print(f"First 20 bytes: {encrypted_data[:20].hex()}")

# Try single-byte XOR keys
print("\nTrying single-byte XOR keys...")
for key in range(256):
    decrypted = try_xor_decrypt(encrypted_data, key)
    if is_printable_text(decrypted):
        print(f"Possible decryption with key {key} (0x{key:02x}):")
        print(decrypted.decode('utf-8', errors='ignore')[:200])
        print("=" * 50)

# Try common string keys
print("\nTrying common string keys...")
common_keys = [
    "password", "key", "secret", "flag", "ctf", "bsides", "blr", "enc", "desktop",
    "123", "abc", "test", "admin", "root", "user", "bsides_blr", "encryption"
]

for key in common_keys:
    decrypted = try_xor_decrypt(encrypted_data, key)
    if is_printable_text(decrypted):
        print(f"Possible decryption with key '{key}':")
        print(decrypted.decode('utf-8', errors='ignore')[:200])
        print("=" * 50)

# Try to look for patterns in the encrypted data
print("\nAnalyzing patterns...")
print(f"Byte frequency analysis (first 10 most common bytes):")
from collections import Counter
byte_freq = Counter(encrypted_data)
for byte, count in byte_freq.most_common(10):
    print(f"0x{byte:02x}: {count} times")
