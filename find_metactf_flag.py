#!/usr/bin/env python3

# Now that we know the flag format is METACTF{...}, let's search for it specifically

def xor_decrypt(data, key):
    if isinstance(key, str):
        key = key.encode()
    elif isinstance(key, int):
        key = bytes([key])
    
    result = bytearray()
    for i, byte in enumerate(data):
        result.append(byte ^ key[i % len(key)])
    return bytes(result)

# Read the encrypted file
with open('desktop.enc', 'rb') as f:
    encrypted_data = f.read()

print("Searching for METACTF{...} flag pattern...")

# Try single-byte XOR keys
for key in range(256):
    decrypted = xor_decrypt(encrypted_data, key)
    try:
        text = decrypted.decode('utf-8', errors='ignore')
        
        # Look for METACTF pattern
        if 'METACTF{' in text:
            print(f"\n*** FOUND METACTF FLAG with XOR key {key} (0x{key:02x}) ***")
            print(f"Full text: {text}")
            
            # Extract just the flag
            start = text.find('METACTF{')
            if start != -1:
                end = text.find('}', start)
                if end != -1:
                    flag = text[start:end+1]
                    print(f"\nFLAG: {flag}")
            
            with open(f'metactf_flag_xor_{key}.txt', 'w') as f:
                f.write(text)
            exit(0)
                
    except:
        continue

# Try multi-byte XOR keys
print("\nTrying multi-byte XOR keys...")
common_keys = [
    'key', 'password', 'secret', 'flag', 'ctf', 'bsides', 'desktop', 'enc',
    'admin', 'root', 'user', 'test', '123', 'abc', 'xyz', 'qwerty',
    'bsides_blr', 'bangalore', 'india', 'challenge', 'reverse', 'metactf'
]

for key in common_keys:
    decrypted = xor_decrypt(encrypted_data, key)
    try:
        text = decrypted.decode('utf-8', errors='ignore')
        
        if 'METACTF{' in text:
            print(f"\n*** FOUND METACTF FLAG with key '{key}' ***")
            print(f"Full text: {text}")
            
            # Extract just the flag
            start = text.find('METACTF{')
            if start != -1:
                end = text.find('}', start)
                if end != -1:
                    flag = text[start:end+1]
                    print(f"\nFLAG: {flag}")
            
            with open(f'metactf_flag_key_{key}.txt', 'w') as f:
                f.write(text)
            exit(0)
                
    except:
        continue

# Try position-based XOR
print("\nTrying position-based XOR...")
position_functions = [
    ("i", lambda i: i % 256),
    ("i+1", lambda i: (i + 1) % 256),
    ("i*2", lambda i: (i * 2) % 256),
    ("i^0xAA", lambda i: (i ^ 0xAA) % 256),
    ("i+42", lambda i: (i + 42) % 256),
    ("i*i", lambda i: (i * i) % 256),
    ("(i+1)*(i+1)", lambda i: ((i + 1) * (i + 1)) % 256),
]

for func_name, func in position_functions:
    decrypted = bytearray()
    for i, byte in enumerate(encrypted_data):
        key = func(i)
        decrypted.append(byte ^ key)
    
    try:
        text = bytes(decrypted).decode('utf-8', errors='ignore')
        
        if 'METACTF{' in text:
            print(f"\n*** FOUND METACTF FLAG with position function {func_name} ***")
            print(f"Full text: {text}")
            
            # Extract just the flag
            start = text.find('METACTF{')
            if start != -1:
                end = text.find('}', start)
                if end != -1:
                    flag = text[start:end+1]
                    print(f"\nFLAG: {flag}")
            
            with open(f'metactf_flag_pos_{func_name.replace("*", "x").replace("^", "xor").replace("+", "plus")}.txt', 'w') as f:
                f.write(text)
            exit(0)
                
    except:
        continue

# Try Caesar cipher
print("\nTrying Caesar cipher...")
for shift in range(1, 26):
    decrypted = bytearray()
    for byte in encrypted_data:
        if 32 <= byte <= 126:  # Printable ASCII
            new_byte = ((byte - 32 + shift) % 95) + 32
        else:
            new_byte = byte
        decrypted.append(new_byte)
    
    try:
        text = bytes(decrypted).decode('utf-8', errors='ignore')
        
        if 'METACTF{' in text:
            print(f"\n*** FOUND METACTF FLAG with Caesar shift {shift} ***")
            print(f"Full text: {text}")
            
            # Extract just the flag
            start = text.find('METACTF{')
            if start != -1:
                end = text.find('}', start)
                if end != -1:
                    flag = text[start:end+1]
                    print(f"\nFLAG: {flag}")
            
            with open(f'metactf_flag_caesar_{shift}.txt', 'w') as f:
                f.write(text)
            exit(0)
                
    except:
        continue

# Try some additional methods
print("\nTrying additional decryption methods...")

# Try simple addition/subtraction
for operation in ['add', 'sub']:
    for key in range(1, 256):
        decrypted = bytearray()
        for byte in encrypted_data:
            if operation == 'add':
                new_byte = (byte + key) % 256
            else:
                new_byte = (byte - key) % 256
            decrypted.append(new_byte)
        
        try:
            text = bytes(decrypted).decode('utf-8', errors='ignore')
            
            if 'METACTF{' in text:
                print(f"\n*** FOUND METACTF FLAG with {operation} {key} ***")
                print(f"Full text: {text}")
                
                # Extract just the flag
                start = text.find('METACTF{')
                if start != -1:
                    end = text.find('}', start)
                    if end != -1:
                        flag = text[start:end+1]
                        print(f"\nFLAG: {flag}")
                
                with open(f'metactf_flag_{operation}_{key}.txt', 'w') as f:
                    f.write(text)
                exit(0)
                    
        except:
            continue

print("\nNo METACTF{...} pattern found with standard methods.")
print("The encryption might be more complex.")
