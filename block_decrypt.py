#!/usr/bin/env python3

# Since the file size is divisible by 16, let's try block-based decryption
# Common block sizes are 8, 16, 32 bytes

def try_block_xor(data, block_size, key):
    """Try XOR decryption with a repeating key per block"""
    result = bytearray()
    for i in range(0, len(data), block_size):
        block = data[i:i+block_size]
        for j, byte in enumerate(block):
            result.append(byte ^ key[j % len(key)])
    return bytes(result)

def try_block_add_sub(data, block_size, operation, key):
    """Try add/subtract operations per block"""
    result = bytearray()
    for i in range(0, len(data), block_size):
        block = data[i:i+block_size]
        for j, byte in enumerate(block):
            if operation == 'add':
                result.append((byte + key) % 256)
            else:  # subtract
                result.append((byte - key) % 256)
    return bytes(result)

def try_reverse_blocks(data, block_size):
    """Try reversing each block"""
    result = bytearray()
    for i in range(0, len(data), block_size):
        block = data[i:i+block_size]
        result.extend(block[::-1])
    return bytes(result)

def try_rotate_blocks(data, block_size, rotation):
    """Try rotating bytes within each block"""
    result = bytearray()
    for i in range(0, len(data), block_size):
        block = data[i:i+block_size]
        rotated = block[rotation:] + block[:rotation]
        result.extend(rotated)
    return bytes(result)

# Read the encrypted file
with open('desktop.enc', 'rb') as f:
    encrypted_data = f.read()

print(f"Trying block-based decryption methods...")

# Try different block sizes
for block_size in [4, 8, 16, 32]:
    print(f"\nTrying block size {block_size}:")
    
    # Try XOR with different keys
    for key_byte in [0x42, 0x23, 0xAA, 0x55, 0xFF]:
        key = bytes([key_byte])
        decrypted = try_block_xor(encrypted_data, block_size, key)
        try:
            text = decrypted.decode('utf-8', errors='ignore')
            if any(word in text.lower() for word in ['flag', 'bsides', 'ctf', 'the', 'and', 'password']):
                print(f"  XOR key 0x{key_byte:02x}: {text[:100]}...")
                with open(f'block_xor_{block_size}_{key_byte:02x}.txt', 'w') as f:
                    f.write(text)
        except:
            pass
    
    # Try add/subtract operations
    for key in [1, 42, 128, 255]:
        for op in ['add', 'sub']:
            decrypted = try_block_add_sub(encrypted_data, block_size, op, key)
            try:
                text = decrypted.decode('utf-8', errors='ignore')
                if any(word in text.lower() for word in ['flag', 'bsides', 'ctf', 'the', 'and', 'password']):
                    print(f"  {op} {key}: {text[:100]}...")
                    with open(f'block_{op}_{block_size}_{key}.txt', 'w') as f:
                        f.write(text)
            except:
                pass
    
    # Try reversing blocks
    decrypted = try_reverse_blocks(encrypted_data, block_size)
    try:
        text = decrypted.decode('utf-8', errors='ignore')
        if any(word in text.lower() for word in ['flag', 'bsides', 'ctf', 'the', 'and', 'password']):
            print(f"  Reverse blocks: {text[:100]}...")
            with open(f'block_reverse_{block_size}.txt', 'w') as f:
                f.write(text)
    except:
        pass
    
    # Try rotating blocks
    for rotation in range(1, min(block_size, 8)):
        decrypted = try_rotate_blocks(encrypted_data, block_size, rotation)
        try:
            text = decrypted.decode('utf-8', errors='ignore')
            if any(word in text.lower() for word in ['flag', 'bsides', 'ctf', 'the', 'and', 'password']):
                print(f"  Rotate {rotation}: {text[:100]}...")
                with open(f'block_rotate_{block_size}_{rotation}.txt', 'w') as f:
                    f.write(text)
        except:
            pass

# Let's also try some more complex patterns
print("\nTrying position-based XOR...")
for key_pattern in [lambda i: i % 256, lambda i: (i * 42) % 256, lambda i: (i ^ 0xAA) % 256]:
    decrypted = bytearray()
    for i, byte in enumerate(encrypted_data):
        key = key_pattern(i)
        decrypted.append(byte ^ key)
    
    try:
        text = bytes(decrypted).decode('utf-8', errors='ignore')
        if any(word in text.lower() for word in ['flag', 'bsides', 'ctf', 'the', 'and', 'password']):
            print(f"  Position-based pattern found: {text[:100]}...")
            with open(f'position_based.txt', 'w') as f:
                f.write(text)
    except:
        pass

print("\nDone trying block-based methods.")
