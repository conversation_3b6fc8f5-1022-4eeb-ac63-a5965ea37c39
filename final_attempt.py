#!/usr/bin/env python3
import base64
import zlib

# Let's manually try to decode the base64 string we found
# The lambda function was: lambda __: __import__('zlib').decompress(__import__('base64').b64decode(__[:-1]))

with open('extracted_b64.txt', 'r') as f:
    b64_string = f.read().strip()

print(f"Base64 string length: {len(b64_string)}")

# The lambda suggests we should remove the last character, then decode and decompress
# Let's try this step by step

# Step 1: Remove last character
trimmed = b64_string[:-1]
print(f"After removing last char: {len(trimmed)}")

# Step 2: Try to decode base64
try:
    # Remove any leading = characters
    clean_b64 = trimmed.lstrip('=')
    decoded = base64.b64decode(clean_b64)
    print(f"Base64 decoded successfully: {len(decoded)} bytes")
    
    # Step 3: Try to decompress with zlib
    try:
        decompressed = zlib.decompress(decoded)
        print(f"Zlib decompressed successfully: {len(decompressed)} bytes")
        
        # Try to decode as text
        source_code = decompressed.decode('utf-8')
        print("SUCCESS! Found the source code:")
        print("=" * 60)
        print(source_code)
        print("=" * 60)
        
        # Save the source code
        with open('encryption_source.py', 'w') as f:
            f.write(source_code)
        print("Saved source code to encryption_source.py")
        
    except Exception as e:
        print(f"Zlib decompression failed: {e}")
        
        # Maybe it's not compressed, try as raw text
        try:
            text = decoded.decode('utf-8')
            print("Raw decoded text:")
            print(text)
        except:
            print("Not valid UTF-8 text either")
            
except Exception as e:
    print(f"Base64 decoding failed: {e}")

# If the above doesn't work, let's try some variations
print("\nTrying variations...")

variations = [
    ("Original", b64_string),
    ("Remove last 2 chars", b64_string[:-2]),
    ("Remove last 3 chars", b64_string[:-3]),
    ("Add padding", b64_string + "="),
    ("Add more padding", b64_string + "=="),
    ("Remove first char", b64_string[1:]),
]

for name, variant in variations:
    try:
        clean = variant.lstrip('=')
        decoded = base64.b64decode(clean)
        
        # Try zlib decompression
        try:
            decompressed = zlib.decompress(decoded)
            text = decompressed.decode('utf-8')
            if 'def ' in text or 'import ' in text or 'class ' in text:
                print(f"\nSUCCESS with {name}!")
                print(text[:200])
                with open(f'source_{name.replace(" ", "_")}.py', 'w') as f:
                    f.write(text)
                break
        except:
            pass
            
        # Try raw decode
        try:
            text = decoded.decode('utf-8')
            if 'def ' in text or 'import ' in text or 'class ' in text:
                print(f"\nSUCCESS (raw) with {name}!")
                print(text[:200])
                with open(f'source_raw_{name.replace(" ", "_")}.py', 'w') as f:
                    f.write(text)
                break
        except:
            pass
            
    except:
        continue

print("Done trying variations.")
