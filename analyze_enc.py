#!/usr/bin/env python3
import base64
import zlib

# The lambda function from the bytecode seems to be:
# lambda __: __import__('zlib').decompress(__import__('base64').b64decode(__[:-1]))

# Let's try to understand what this means
# It takes the base64 string, removes the last character, decodes it, then decompresses

# Read the base64 string
with open('extracted_b64.txt', 'r') as f:
    b64_string = f.read().strip()

print(f"Original length: {len(b64_string)}")

# Try removing the last character as the lambda suggests
b64_trimmed = b64_string[:-1]
print(f"Trimmed length: {len(b64_trimmed)}")

try:
    # Remove leading = and try to decode
    b64_clean = b64_trimmed.lstrip("=")
    decoded = base64.b64decode(b64_clean)
    print(f"Decoded length: {len(decoded)}")
    
    # Try to decompress
    decompressed = zlib.decompress(decoded)
    print("SUCCESS! Decompressed source code:")
    print(decompressed.decode('utf-8'))
    
    # Save the source code
    with open('enc_source.py', 'w') as f:
        f.write(decompressed.decode('utf-8'))
    print("\nSaved source code to enc_source.py")
    
except Exception as e:
    print(f"Error: {e}")
    
    # Let's try different approaches
    print("\nTrying different trimming approaches...")
    for i in range(1, 10):
        try:
            b64_test = b64_string[:-i].lstrip("=")
            decoded = base64.b64decode(b64_test)
            decompressed = zlib.decompress(decoded)
            print(f"SUCCESS with trimming {i} characters!")
            print(decompressed.decode('utf-8'))
            break
        except:
            continue
