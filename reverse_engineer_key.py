#!/usr/bin/env python3

# Let's try to reverse engineer the key by assuming METACTF{ appears somewhere in the file

# Read the encrypted file
with open('desktop.enc', 'rb') as f:
    encrypted_data = f.read()

target = b"METACTF{"
print(f"Looking for pattern: {target}")
print(f"Encrypted file size: {len(encrypted_data)} bytes")
print(f"First 20 bytes: {encrypted_data[:20].hex()}")

# Try to find where METACTF{ might be by testing different positions
for pos in range(len(encrypted_data) - len(target) + 1):
    encrypted_chunk = encrypted_data[pos:pos + len(target)]
    
    # Calculate what the XOR key would need to be
    potential_key = bytearray()
    for i in range(len(target)):
        key_byte = encrypted_chunk[i] ^ target[i]
        potential_key.append(key_byte)
    
    # Check if this key produces a consistent pattern
    # Try single-byte key (if all bytes are the same)
    if len(set(potential_key)) == 1:
        key = potential_key[0]
        print(f"\nTrying single-byte XOR key 0x{key:02x} at position {pos}")
        
        # Decrypt the entire file with this key
        decrypted = bytes([b ^ key for b in encrypted_data])
        try:
            text = decrypted.decode('utf-8', errors='ignore')
            if 'METACTF{' in text:
                print(f"*** SUCCESS! Found METACTF with single-byte key 0x{key:02x} ***")
                print(f"Decrypted text: {text}")
                
                # Extract the flag
                start = text.find('METACTF{')
                if start != -1:
                    end = text.find('}', start)
                    if end != -1:
                        flag = text[start:end+1]
                        print(f"\nFLAG: {flag}")
                
                with open('final_flag.txt', 'w') as f:
                    f.write(text)
                exit(0)
        except:
            pass
    
    # Try multi-byte repeating key
    # Test if this key pattern repeats well
    key_pattern = bytes(potential_key)
    print(f"\nTrying multi-byte key pattern at position {pos}: {key_pattern.hex()}")
    
    # Decrypt with repeating key
    decrypted = bytearray()
    for i, byte in enumerate(encrypted_data):
        key_byte = key_pattern[i % len(key_pattern)]
        decrypted.append(byte ^ key_byte)
    
    try:
        text = bytes(decrypted).decode('utf-8', errors='ignore')
        if 'METACTF{' in text:
            print(f"*** SUCCESS! Found METACTF with multi-byte key {key_pattern.hex()} ***")
            print(f"Decrypted text: {text}")
            
            # Extract the flag
            start = text.find('METACTF{')
            if start != -1:
                end = text.find('}', start)
                if end != -1:
                    flag = text[start:end+1]
                    print(f"\nFLAG: {flag}")
            
            with open('final_flag.txt', 'w') as f:
                f.write(text)
            exit(0)
    except:
        pass

# If we didn't find it by position, let's try some other approaches
print("\nTrying known key patterns...")

# Maybe the key is related to the position
for pos in range(len(encrypted_data) - len(target) + 1):
    encrypted_chunk = encrypted_data[pos:pos + len(target)]
    
    # Try position-based key
    potential_key = bytearray()
    for i in range(len(target)):
        # Key might be position + some offset
        for offset in [0, 1, 42, pos]:
            key_byte = (pos + i + offset) % 256
            if encrypted_chunk[i] ^ key_byte == target[i]:
                # This might work, let's test the full decryption
                decrypted = bytearray()
                for j, byte in enumerate(encrypted_data):
                    decrypt_key = (pos + j + offset) % 256
                    decrypted.append(byte ^ decrypt_key)
                
                try:
                    text = bytes(decrypted).decode('utf-8', errors='ignore')
                    if 'METACTF{' in text:
                        print(f"*** SUCCESS! Found METACTF with position-based key (pos={pos}, offset={offset}) ***")
                        print(f"Decrypted text: {text}")
                        
                        # Extract the flag
                        start = text.find('METACTF{')
                        if start != -1:
                            end = text.find('}', start)
                            if end != -1:
                                flag = text[start:end+1]
                                print(f"\nFLAG: {flag}")
                        
                        with open('final_flag.txt', 'w') as f:
                            f.write(text)
                        exit(0)
                except:
                    pass

print("\nCould not find METACTF pattern with reverse engineering approach.")
print("The encryption might be more complex or the flag might not start with METACTF.")
