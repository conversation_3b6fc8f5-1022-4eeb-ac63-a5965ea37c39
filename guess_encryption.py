#!/usr/bin/env python3

# Let's try to guess what the encryption algorithm might be
# Based on the file name "desktop.enc", it's probably encrypting a file called "desktop"

# Let's create some test content and see if we can match the encryption pattern
test_contents = [
    "This is a test file for the CTF challenge.",
    "flag{this_is_a_test_flag}",
    "BSides Bangalore CTF 2024",
    "The quick brown fox jumps over the lazy dog.",
    "Hello, World!",
    "Desktop file content here.",
    "#!/bin/bash\necho 'Hello World'",
    "<?xml version='1.0'?>\n<root>test</root>",
]

def simple_xor_encrypt(data, key):
    if isinstance(data, str):
        data = data.encode()
    if isinstance(key, str):
        key = key.encode()
    
    result = bytearray()
    for i, byte in enumerate(data):
        result.append(byte ^ key[i % len(key)])
    return bytes(result)

def position_based_encrypt(data, func):
    if isinstance(data, str):
        data = data.encode()
    
    result = bytearray()
    for i, byte in enumerate(data):
        key = func(i) % 256
        result.append(byte ^ key)
    return bytes(result)

# Read the actual encrypted file
with open('desktop.enc', 'rb') as f:
    encrypted_data = f.read()

print(f"Encrypted file size: {len(encrypted_data)} bytes")

# Try different encryption methods on test data
encryption_methods = [
    ("XOR with 'key'", lambda data: simple_xor_encrypt(data, 'key')),
    ("XOR with 'password'", lambda data: simple_xor_encrypt(data, 'password')),
    ("XOR with 'desktop'", lambda data: simple_xor_encrypt(data, 'desktop')),
    ("XOR with 'bsides'", lambda data: simple_xor_encrypt(data, 'bsides')),
    ("XOR with position", lambda data: position_based_encrypt(data, lambda i: i)),
    ("XOR with position*2", lambda data: position_based_encrypt(data, lambda i: i*2)),
    ("XOR with position^0xAA", lambda data: position_based_encrypt(data, lambda i: i^0xAA)),
]

# For each test content, try each encryption method
for content in test_contents:
    content_bytes = content.encode() if isinstance(content, str) else content
    
    # Skip if the size doesn't match
    if len(content_bytes) != len(encrypted_data):
        continue
    
    print(f"\nTesting content: '{content[:50]}...' (size: {len(content_bytes)})")
    
    for method_name, encrypt_func in encryption_methods:
        try:
            encrypted_test = encrypt_func(content)
            
            # Check if the encrypted test matches the actual encrypted data
            if encrypted_test == encrypted_data:
                print(f"*** MATCH FOUND! ***")
                print(f"Content: {content}")
                print(f"Method: {method_name}")
                print("This is the decrypted content!")
                with open('decrypted_content.txt', 'w') as f:
                    f.write(content)
                exit(0)
            
            # Check for partial matches (first few bytes)
            if len(encrypted_test) >= 20 and encrypted_test[:20] == encrypted_data[:20]:
                print(f"Partial match (first 20 bytes) with {method_name}")
                
        except Exception as e:
            continue

# If no exact matches, let's try to decrypt with common methods
print("\nNo exact matches found. Trying to decrypt with common methods...")

common_keys = ['key', 'password', 'desktop', 'bsides', 'flag', 'ctf', 'enc']

for key in common_keys:
    decrypted = simple_xor_encrypt(encrypted_data, key)
    try:
        text = decrypted.decode('utf-8', errors='ignore')
        # Check if it looks like meaningful text
        if len(text.strip()) > 10 and any(c.isalpha() for c in text):
            print(f"\nPossible decryption with key '{key}':")
            print(f"Text: {text[:100]}...")
            
            # Check for common words or patterns
            if any(word in text.lower() for word in ['flag', 'bsides', 'ctf', 'the', 'and', 'is', 'to']):
                print("*** This looks promising! ***")
                with open(f'decrypt_key_{key}.txt', 'w') as f:
                    f.write(text)
    except:
        pass

# Try position-based decryption
print("\nTrying position-based decryption...")
position_functions = [
    ("i", lambda i: i),
    ("i*2", lambda i: i*2),
    ("i^0xAA", lambda i: i^0xAA),
    ("i+42", lambda i: i+42),
    ("i*i", lambda i: i*i),
]

for func_name, func in position_functions:
    decrypted = bytearray()
    for i, byte in enumerate(encrypted_data):
        key = func(i) % 256
        decrypted.append(byte ^ key)
    
    try:
        text = bytes(decrypted).decode('utf-8', errors='ignore')
        if len(text.strip()) > 10 and any(c.isalpha() for c in text):
            print(f"\nPossible decryption with position function {func_name}:")
            print(f"Text: {text[:100]}...")
            
            if any(word in text.lower() for word in ['flag', 'bsides', 'ctf', 'the', 'and', 'is', 'to']):
                print("*** This looks promising! ***")
                with open(f'decrypt_pos_{func_name.replace("*", "x").replace("^", "xor")}.txt', 'w') as f:
                    f.write(text)
    except:
        pass

print("\nDone with decryption attempts.")
