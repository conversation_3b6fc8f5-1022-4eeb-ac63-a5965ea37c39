#!/usr/bin/env python3

# Let's try a simple approach - create a test encryption and see if we can reverse it
# Common CTF encryption patterns

def simple_xor_encrypt(data, key):
    """Simple XOR encryption"""
    if isinstance(key, str):
        key = key.encode()
    result = bytearray()
    for i, byte in enumerate(data):
        result.append(byte ^ key[i % len(key)])
    return bytes(result)

def simple_add_encrypt(data, key):
    """Simple addition encryption"""
    result = bytearray()
    for byte in data:
        result.append((byte + key) % 256)
    return bytes(result)

def simple_sub_encrypt(data, key):
    """Simple subtraction encryption"""
    result = bytearray()
    for byte in data:
        result.append((byte - key) % 256)
    return bytes(result)

# Read the encrypted file
with open('desktop.enc', 'rb') as f:
    encrypted_data = f.read()

print(f"Encrypted data length: {len(encrypted_data)}")
print(f"First 20 bytes: {encrypted_data[:20].hex()}")

# Let's try to guess what the original file might be
# Common file types: text file, image, document, etc.

# Try different decryption methods
methods = [
    ("XOR with 'key'", lambda data: simple_xor_encrypt(data, 'key')),
    ("XOR with 'password'", lambda data: simple_xor_encrypt(data, 'password')),
    ("XOR with 'flag'", lambda data: simple_xor_encrypt(data, 'flag')),
    ("XOR with 'bsides'", lambda data: simple_xor_encrypt(data, 'bsides')),
    ("XOR with 'desktop'", lambda data: simple_xor_encrypt(data, 'desktop')),
    ("XOR with 0x42", lambda data: simple_xor_encrypt(data, b'\x42')),
    ("Add 1", lambda data: simple_sub_encrypt(data, 1)),
    ("Subtract 1", lambda data: simple_add_encrypt(data, 1)),
    ("Add 42", lambda data: simple_sub_encrypt(data, 42)),
    ("Subtract 42", lambda data: simple_add_encrypt(data, 42)),
]

for name, decrypt_func in methods:
    try:
        decrypted = decrypt_func(encrypted_data)
        
        # Check if it looks like text
        try:
            text = decrypted.decode('utf-8')
            if any(word in text.lower() for word in ['flag', 'bsides', 'ctf', 'password', 'the', 'and', 'is']):
                print(f"\n*** POSSIBLE SUCCESS with {name} ***")
                print(f"Decrypted text: {text[:200]}")
                with open(f'decrypted_{name.replace(" ", "_").replace("/", "_")}.txt', 'w') as f:
                    f.write(text)
                print("=" * 60)
        except:
            pass
            
        # Check if it looks like a known file format
        file_signatures = {
            b'\x89PNG': 'PNG image',
            b'\xFF\xD8\xFF': 'JPEG image',
            b'GIF8': 'GIF image',
            b'%PDF': 'PDF document',
            b'PK\x03\x04': 'ZIP archive',
            b'#!/bin/': 'Shell script',
            b'<?xml': 'XML document',
            b'<html': 'HTML document',
            b'BM': 'BMP image',
        }
        
        for sig, desc in file_signatures.items():
            if decrypted.startswith(sig):
                print(f"\n*** POSSIBLE SUCCESS with {name} ***")
                print(f"Decrypted file appears to be: {desc}")
                with open(f'decrypted_{name.replace(" ", "_").replace("/", "_")}.bin', 'wb') as f:
                    f.write(decrypted)
                print("=" * 60)
                break
                
    except Exception as e:
        continue

# Let's also try to see if the file is just encoded in some way
print("\nTrying encoding methods...")
import base64
import codecs

encoding_methods = [
    ("Base64", lambda data: base64.b64decode(data)),
    ("Hex", lambda data: bytes.fromhex(data.decode('ascii'))),
    ("ROT13", lambda data: codecs.decode(data.decode('ascii'), 'rot13').encode()),
]

for name, decode_func in encoding_methods:
    try:
        decoded = decode_func(encrypted_data)
        text = decoded.decode('utf-8')
        if any(word in text.lower() for word in ['flag', 'bsides', 'ctf', 'password']):
            print(f"\n*** POSSIBLE SUCCESS with {name} encoding ***")
            print(f"Decoded text: {text[:200]}")
            print("=" * 60)
    except:
        continue
